import { getActiveOrganization } from "@saas/auth/lib/server";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { PageTabs } from "@saas/shared/components/PageTabs";
import { ConversationsOverview } from "@/modules/saas/conversations/components/ConversationsOverview";
import { ConversationsList } from "@/modules/saas/conversations/components/ConversationsList";
import { ConversationsReports } from "@/modules/saas/conversations/components/ConversationsReports";
import { ConversationsSettings } from "@/modules/saas/conversations/components/ConversationsSettings";
import { Button } from "@ui/components/button";
import { DownloadIcon, UsersIcon, MessageSquareIcon } from "lucide-react";
import { notFound } from "next/navigation";

export default async function ConversationsPage({
  params,
}: {
  params: Promise<{ organizationSlug: string }>;
}) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);

  if (!organization) {
    return notFound();
  }

  const tabs = [
    {
      value: "conversations",
      label: "Conversas",
      badge: "23",
      content: <ConversationsList organizationId={organization.id} />,
    },
    {
      value: "overview",
      label: "Visão Geral",
      content: <ConversationsOverview organizationId={organization.id} />,
    },
    {
      value: "reports",
      label: "Relatórios",
      content: <ConversationsReports organizationId={organization.id} />,
    },
    {
      value: "settings",
      label: "Configurações",
      content: <ConversationsSettings organizationId={organization.id} />,
    },
  ];

  return (
    <div className="space-y-6">
      <PageHeader
        title="Conversas"
        subtitle="Gerencie todas as conversas com seus clientes"
        actions={
          <>
            <Button variant="outline">
              <DownloadIcon className="h-4 w-4 mr-2" />
              Exportar
            </Button>
            <Button variant="outline">
              <UsersIcon className="h-4 w-4 mr-2" />
              Contatos
            </Button>
            <Button>
              <MessageSquareIcon className="h-4 w-4 mr-2" />
              Nova Conversa
            </Button>
          </>
        }
      />

      <PageTabs tabs={tabs} defaultValue="conversations" />
    </div>
  );
}
