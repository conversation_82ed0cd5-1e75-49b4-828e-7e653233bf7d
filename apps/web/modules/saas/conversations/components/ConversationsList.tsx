"use client";

import { useState } from "react";
import { Card } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Badge } from "@ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Separator } from "@ui/components/separator";
import {
  SearchIcon,
  FilterIcon,
  PhoneIcon,
  VideoIcon,
  MoreVerticalIcon,
  SendIcon,
  PaperclipIcon,
  SmileIcon,
  StarIcon,
  CheckIcon,
  CheckCheckIcon,
  MessageSquareIcon,
  UserIcon,
  ClockIcon,
  TagIcon,
} from "lucide-react";
import { cn } from "@ui/lib";

interface ConversationsListProps {
  organizationId: string;
}

// Mock data - replace with real API calls
const mockConversations = [
  {
    id: "1",
    contact: {
      id: "1",
      name: "Biomolgene",
      avatar: "B",
      phone: "+55 27 99771-4827",
      email: "<EMAIL>",
      status: "online" as const,
    },
    lastMessage: "Olá! Sou Ana, secretária do ZapVida. Podemos te ajudar sim!",
    timestamp: "9:54 PM",
    unread: true,
    starred: false,
    channel: "whatsapp" as const,
    status: "open" as const,
    assignedTo: "Ana Silva",
    tags: ["VIP", "Médico"],
  },
  {
    id: "2",
    contact: {
      id: "2",
      name: "Cliente Verde",
      avatar: "🟢",
      phone: "+55 27 99999-9999",
      email: "<EMAIL>",
      status: "away" as const,
    },
    lastMessage: "Faz esse exame ai",
    timestamp: "7:52 PM",
    unread: false,
    starred: true,
    channel: "whatsapp" as const,
    status: "pending" as const,
    assignedTo: "João Santos",
    tags: ["Urgente"],
  },
  {
    id: "3",
    contact: {
      id: "3",
      name: "Alessandra S.",
      avatar: "AS",
      phone: "+55 27 88888-8888",
      email: "<EMAIL>",
      status: "offline" as const,
    },
    lastMessage: "Preciso de uma consulta médica urgente",
    timestamp: "4:00 PM",
    unread: false,
    starred: false,
    channel: "sms" as const,
    status: "open" as const,
    assignedTo: null,
    tags: ["Novo Cliente"],
  },
];

const mockMessages = [
  {
    id: "1",
    direction: "inbound" as const,
    content: "Olá! Preciso de uma consulta médica",
    timestamp: "21:51",
    status: "read" as const,
  },
  {
    id: "2",
    direction: "outbound" as const,
    content: "Olá! Sou Ana, secretária do ZapVida. Podemos te ajudar sim!",
    timestamp: "21:52",
    status: "read" as const,
  },
  {
    id: "3",
    direction: "inbound" as const,
    content: "Perfeito! Quais são os preços?",
    timestamp: "21:53",
    status: "read" as const,
  },
  {
    id: "4",
    direction: "outbound" as const,
    content: "Consultas a partir de R$ 80,00 com início rápido, por chat, áudio ou ligação.",
    timestamp: "21:54",
    status: "read" as const,
  },
];

export function ConversationsList({ organizationId }: ConversationsListProps) {
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [message, setMessage] = useState("");
  const [activeFilter, setActiveFilter] = useState<"all" | "unread" | "starred">("all");

  const filteredConversations = mockConversations.filter(conversation => {
    const matchesSearch = conversation.contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         conversation.lastMessage.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesFilter = activeFilter === "all" ||
                         (activeFilter === "unread" && conversation.unread) ||
                         (activeFilter === "starred" && conversation.starred);

    return matchesSearch && matchesFilter;
  });

  const selectedConv = mockConversations.find(c => c.id === selectedConversation);

  const handleSendMessage = () => {
    if (message.trim()) {
      console.log("Enviando mensagem:", message);
      setMessage("");
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "sent":
        return <CheckIcon className="w-4 h-4 text-muted-foreground" />;
      case "delivered":
        return <CheckCheckIcon className="w-4 h-4 text-muted-foreground" />;
      case "read":
        return <CheckCheckIcon className="w-4 h-4 text-blue-500" />;
      default:
        return null;
    }
  };

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case "whatsapp":
        return "💬";
      case "sms":
        return "📱";
      case "email":
        return "📧";
      default:
        return "💬";
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      open: { label: "Aberta", variant: "default" as const },
      pending: { label: "Pendente", variant: "secondary" as const },
      resolved: { label: "Resolvida", variant: "outline" as const },
      closed: { label: "Fechada", variant: "destructive" as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.open;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  return (
    <div className="flex h-[calc(100vh-200px)] bg-background border rounded-lg overflow-hidden">
      {/* Left Sidebar - Conversation List */}
      <div className="w-80 border-r flex flex-col bg-background">
        {/* Search and Filters */}
        <div className="p-4 border-b space-y-4">
          <div className="relative">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="Buscar conversas..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="flex space-x-2">
            {[
              { id: "all", label: "Todas" },
              { id: "unread", label: "Não lidas" },
              { id: "starred", label: "Favoritas" }
            ].map((filter) => (
              <Button
                key={filter.id}
                variant={activeFilter === filter.id ? "default" : "outline"}
                size="sm"
                onClick={() => setActiveFilter(filter.id as any)}
              >
                {filter.label}
              </Button>
            ))}
          </div>
        </div>

        {/* Conversation List */}
        <div className="flex-1 overflow-y-auto">
          {filteredConversations.map((conversation) => (
            <div
              key={conversation.id}
              onClick={() => setSelectedConversation(conversation.id)}
              className={cn(
                "flex items-center gap-3 p-4 border-b cursor-pointer transition-colors hover:bg-muted/50",
                conversation.unread && "bg-primary/5",
                selectedConversation === conversation.id && "bg-muted"
              )}
            >
              {/* Avatar */}
              <div className="relative">
                <Avatar className="w-12 h-12">
                  <AvatarFallback className={cn(
                    "text-white font-medium",
                    conversation.channel === "whatsapp" ? "bg-green-500" :
                    conversation.channel === "sms" ? "bg-blue-500" : "bg-purple-500"
                  )}>
                    {conversation.contact.avatar}
                  </AvatarFallback>
                </Avatar>
                <div className={cn(
                  "absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-background",
                  conversation.contact.status === "online" ? "bg-green-500" :
                  conversation.contact.status === "away" ? "bg-yellow-500" : "bg-muted-foreground"
                )} />
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h4 className="font-medium truncate">
                    {conversation.contact.name}
                  </h4>
                  <div className="flex items-center gap-1">
                    {conversation.starred && (
                      <StarIcon className="w-4 h-4 text-yellow-500 fill-current" />
                    )}
                    <span className="text-xs text-muted-foreground">
                      {conversation.timestamp}
                    </span>
                  </div>
                </div>

                <div className="flex items-center gap-2 mb-2">
                  <span className="text-sm text-muted-foreground truncate">
                    {conversation.lastMessage}
                  </span>
                  <span className="text-xs">
                    {getChannelIcon(conversation.channel)}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1">
                    {getStatusBadge(conversation.status)}
                    {conversation.assignedTo && (
                      <Badge variant="outline" className="text-xs">
                        <UserIcon className="w-3 h-3 mr-1" />
                        {conversation.assignedTo}
                      </Badge>
                    )}
                  </div>
                  {conversation.unread && (
                    <Badge variant="default" className="text-xs">
                      Nova
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Center - Chat Area */}
      <div className="flex-1 flex flex-col">
        {selectedConv ? (
          <>
            {/* Chat Header */}
            <div className="p-4 border-b bg-background">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Avatar className="w-10 h-10">
                    <AvatarFallback className={cn(
                      "text-white font-medium",
                      selectedConv.channel === "whatsapp" ? "bg-green-500" :
                      selectedConv.channel === "sms" ? "bg-blue-500" : "bg-purple-500"
                    )}>
                      {selectedConv.contact.avatar}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold">{selectedConv.contact.name}</h3>
                    <p className="text-sm text-muted-foreground">
                      {selectedConv.contact.status === "online" ? "Online" : "Offline"}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="ghost" size="sm">
                    <PhoneIcon className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <VideoIcon className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <MoreVerticalIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-muted/20">
              {mockMessages.map((msg) => (
                <div
                  key={msg.id}
                  className={cn(
                    "flex",
                    msg.direction === "outbound" ? "justify-end" : "justify-start"
                  )}
                >
                  <div className={cn(
                    "max-w-xs px-4 py-2 rounded-lg",
                    msg.direction === "outbound"
                      ? "bg-primary text-primary-foreground"
                      : "bg-background border shadow-sm"
                  )}>
                    <p className="text-sm">{msg.content}</p>
                    <div className={cn(
                      "flex items-center justify-end gap-1 mt-1",
                      msg.direction === "outbound" ? "text-primary-foreground/70" : "text-muted-foreground"
                    )}>
                      <span className="text-xs">{msg.timestamp}</span>
                      {msg.direction === "outbound" && getStatusIcon(msg.status)}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Message Input */}
            <div className="border-t bg-background p-4">
              <div className="flex items-center gap-2">
                <Button variant="ghost" size="sm">
                  <PaperclipIcon className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="sm">
                  <SmileIcon className="h-4 w-4" />
                </Button>
                <div className="flex-1">
                  <Input
                    placeholder="Digite uma mensagem..."
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
                  />
                </div>
                <Button
                  onClick={handleSendMessage}
                  disabled={!message.trim()}
                  size="sm"
                >
                  <SendIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <MessageSquareIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Selecione uma conversa</h3>
              <p className="text-muted-foreground">
                Escolha uma conversa da lista para começar a responder
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Right Sidebar - Contact Panel */}
      <div className="w-80 border-l bg-background">
        {selectedConv ? (
          <div className="h-full flex flex-col">
            {/* Contact Header */}
            <div className="p-4 border-b text-center">
              <Avatar className="w-16 h-16 mx-auto mb-2">
                <AvatarFallback className={cn(
                  "text-white font-medium text-lg",
                  selectedConv.channel === "whatsapp" ? "bg-green-500" :
                  selectedConv.channel === "sms" ? "bg-blue-500" : "bg-purple-500"
                )}>
                  {selectedConv.contact.avatar}
                </AvatarFallback>
              </Avatar>
              <h3 className="font-semibold">{selectedConv.contact.name}</h3>
              <p className="text-sm text-muted-foreground">{selectedConv.contact.email}</p>
            </div>

            {/* Contact Details */}
            <div className="flex-1 overflow-y-auto p-4 space-y-6">
              <div>
                <h4 className="font-medium mb-3">Informações de Contato</h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <PhoneIcon className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{selectedConv.contact.phone}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm">📧</span>
                    <span className="text-sm">{selectedConv.contact.email}</span>
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <h4 className="font-medium mb-3">Status da Conversa</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Status:</span>
                    {getStatusBadge(selectedConv.status)}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Canal:</span>
                    <span className="text-sm">{getChannelIcon(selectedConv.channel)} {selectedConv.channel}</span>
                  </div>
                  {selectedConv.assignedTo && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Responsável:</span>
                      <span className="text-sm">{selectedConv.assignedTo}</span>
                    </div>
                  )}
                </div>
              </div>

              <Separator />

              <div>
                <h4 className="font-medium mb-3">Tags</h4>
                <div className="flex flex-wrap gap-2">
                  {selectedConv.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary">
                      <TagIcon className="h-3 w-3 mr-1" />
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <UserIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                Selecione uma conversa para ver os detalhes do contato
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
