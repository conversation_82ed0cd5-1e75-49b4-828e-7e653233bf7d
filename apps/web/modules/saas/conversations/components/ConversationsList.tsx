"use client";

import { useState } from "react";
import { Card } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Badge } from "@ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Separator } from "@ui/components/separator";
import {
  SearchIcon,
  FilterIcon,
  PhoneIcon,
  VideoIcon,
  MoreVerticalIcon,
  SendIcon,
  PaperclipIcon,
  SmileIcon,
  StarIcon,
  CheckIcon,
  CheckCheckIcon,
  MessageSquareIcon,
  UserIcon,
  ClockIcon,
  TagIcon,
  MailIcon,
  PlusIcon,
} from "lucide-react";
import { cn } from "@ui/lib";

interface ConversationsListProps {
  organizationId: string;
}

// Mock data - replace with real API calls
const mockConversations = [
  {
    id: "1",
    contact: {
      id: "1",
      name: "Biomolgene",
      avatar: "B",
      phone: "+55 27 99771-4827",
      email: "<EMAIL>",
      status: "online" as const,
    },
    lastMessage: "Olá! Sou Ana, secretária do ZapVida. Podemos te ajudar sim!",
    timestamp: "9:54 PM",
    unread: true,
    starred: false,
    channel: "whatsapp" as const,
    status: "open" as const,
    assignedTo: "Ana Silva",
    tags: ["VIP", "Médico"],
  },
  {
    id: "2",
    contact: {
      id: "2",
      name: "Cliente Verde",
      avatar: "🟢",
      phone: "+55 27 99999-9999",
      email: "<EMAIL>",
      status: "away" as const,
    },
    lastMessage: "Faz esse exame ai",
    timestamp: "7:52 PM",
    unread: false,
    starred: true,
    channel: "whatsapp" as const,
    status: "pending" as const,
    assignedTo: "João Santos",
    tags: ["Urgente"],
  },
  {
    id: "3",
    contact: {
      id: "3",
      name: "Alessandra S.",
      avatar: "AS",
      phone: "+55 27 88888-8888",
      email: "<EMAIL>",
      status: "offline" as const,
    },
    lastMessage: "Preciso de uma consulta médica urgente",
    timestamp: "4:00 PM",
    unread: false,
    starred: false,
    channel: "sms" as const,
    status: "open" as const,
    assignedTo: null,
    tags: ["Novo Cliente"],
  },
];

const mockMessages = [
  {
    id: "1",
    direction: "inbound" as const,
    content: "Olá! Preciso de uma consulta médica",
    timestamp: "21:51",
    status: "read" as const,
  },
  {
    id: "2",
    direction: "outbound" as const,
    content: "Olá! Sou Ana, secretária do ZapVida. Podemos te ajudar sim!",
    timestamp: "21:52",
    status: "read" as const,
  },
  {
    id: "3",
    direction: "inbound" as const,
    content: "Perfeito! Quais são os preços?",
    timestamp: "21:53",
    status: "read" as const,
  },
  {
    id: "4",
    direction: "outbound" as const,
    content: "Consultas a partir de R$ 80,00 com início rápido, por chat, áudio ou ligação.",
    timestamp: "21:54",
    status: "read" as const,
  },
];

export function ConversationsList({ organizationId }: ConversationsListProps) {
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [message, setMessage] = useState("");
  const [activeFilter, setActiveFilter] = useState<"all" | "unread" | "starred">("all");

  const filteredConversations = mockConversations.filter(conversation => {
    const matchesSearch = conversation.contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         conversation.lastMessage.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesFilter = activeFilter === "all" ||
                         (activeFilter === "unread" && conversation.unread) ||
                         (activeFilter === "starred" && conversation.starred);

    return matchesSearch && matchesFilter;
  });

  const selectedConv = mockConversations.find(c => c.id === selectedConversation);

  const handleSendMessage = () => {
    if (message.trim()) {
      console.log("Enviando mensagem:", message);
      setMessage("");
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "sent":
        return <CheckIcon className="w-4 h-4 text-muted-foreground" />;
      case "delivered":
        return <CheckCheckIcon className="w-4 h-4 text-muted-foreground" />;
      case "read":
        return <CheckCheckIcon className="w-4 h-4 text-blue-500" />;
      default:
        return null;
    }
  };

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case "whatsapp":
        return "💬";
      case "sms":
        return "📱";
      case "email":
        return "📧";
      default:
        return "💬";
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      open: { label: "Aberta", variant: "default" as const },
      pending: { label: "Pendente", variant: "secondary" as const },
      resolved: { label: "Resolvida", variant: "outline" as const },
      closed: { label: "Fechada", variant: "destructive" as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.open;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  return (
    <div className="flex h-[calc(100vh-180px)] bg-background border rounded-lg overflow-hidden">
      {/* Left Sidebar - Conversation List */}
      <div className="w-80 border-r flex flex-col bg-background">
        {/* Search and Filters Header */}
        <div className="p-4 border-b bg-muted/30">
          <div className="relative mb-3">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="Buscar conversas..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-background"
            />
          </div>

          {/* Filter Tabs */}
          <div className="flex space-x-1">
            {[
              { id: "all", label: "Todas", count: mockConversations.length },
              { id: "unread", label: "Não lidas", count: mockConversations.filter(c => c.unread).length },
              { id: "starred", label: "Favoritas", count: mockConversations.filter(c => c.starred).length }
            ].map((filter) => (
              <Button
                key={filter.id}
                variant={activeFilter === filter.id ? "default" : "ghost"}
                size="sm"
                className={cn(
                  "flex-1 justify-center text-xs",
                  activeFilter === filter.id && "bg-primary text-primary-foreground"
                )}
                onClick={() => setActiveFilter(filter.id as any)}
              >
                {filter.label}
                {filter.count > 0 && (
                  <Badge variant="secondary" className="ml-1 text-xs">
                    {filter.count}
                  </Badge>
                )}
              </Button>
            ))}
          </div>
        </div>

        {/* Conversation List */}
        <div className="flex-1 overflow-y-auto">
          {filteredConversations.map((conversation) => (
            <div
              key={conversation.id}
              onClick={() => setSelectedConversation(conversation.id)}
              className={cn(
                "flex items-start gap-3 p-3 border-b border-border/50 cursor-pointer transition-all duration-200 hover:bg-muted/30",
                conversation.unread && "bg-primary/5 border-l-2 border-l-primary",
                selectedConversation === conversation.id && "bg-muted/50 border-l-2 border-l-primary"
              )}
            >
              {/* Avatar */}
              <div className="relative flex-shrink-0 mt-1">
                <Avatar className="w-10 h-10">
                  <AvatarFallback className={cn(
                    "text-white font-medium text-sm",
                    conversation.channel === "whatsapp" ? "bg-green-500" :
                    conversation.channel === "sms" ? "bg-blue-500" : "bg-purple-500"
                  )}>
                    {conversation.contact.avatar}
                  </AvatarFallback>
                </Avatar>
                <div className={cn(
                  "absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-background",
                  conversation.contact.status === "online" ? "bg-green-500" :
                  conversation.contact.status === "away" ? "bg-yellow-500" : "bg-muted-foreground"
                )} />
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h4 className="font-medium text-sm truncate">
                    {conversation.contact.name}
                  </h4>
                  <div className="flex items-center gap-1 flex-shrink-0">
                    <span className="text-xs text-muted-foreground">
                      {conversation.timestamp}
                    </span>
                    {conversation.starred && (
                      <StarIcon className="w-3 h-3 text-yellow-500 fill-current" />
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-2 mb-2">
                  <span className="text-xs text-muted-foreground truncate flex-1">
                    {conversation.lastMessage}
                  </span>
                  <span className="text-xs flex-shrink-0">
                    {getChannelIcon(conversation.channel)}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1">
                    {getStatusBadge(conversation.status)}
                  </div>
                  <div className="flex items-center gap-1">
                    {conversation.assignedTo && (
                      <Badge variant="outline" className="text-xs px-1 py-0">
                        {conversation.assignedTo.split(' ')[0]}
                      </Badge>
                    )}
                    {conversation.unread && (
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Center - Chat Area */}
      <div className="flex-1 flex flex-col bg-background">
        {selectedConv ? (
          <>
            {/* Chat Header */}
            <div className="px-6 py-4 border-b bg-background/95 backdrop-blur">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Avatar className="w-10 h-10">
                    <AvatarFallback className={cn(
                      "text-white font-medium",
                      selectedConv.channel === "whatsapp" ? "bg-green-500" :
                      selectedConv.channel === "sms" ? "bg-blue-500" : "bg-purple-500"
                    )}>
                      {selectedConv.contact.avatar}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold text-base">{selectedConv.contact.name}</h3>
                    <div className="flex items-center gap-2">
                      <div className={cn(
                        "w-2 h-2 rounded-full",
                        selectedConv.contact.status === "online" ? "bg-green-500" :
                        selectedConv.contact.status === "away" ? "bg-yellow-500" : "bg-muted-foreground"
                      )} />
                      <p className="text-sm text-muted-foreground">
                        {selectedConv.contact.status === "online" ? "Online agora" :
                         selectedConv.contact.status === "away" ? "Ausente" : "Offline"}
                      </p>
                      <span className="text-xs text-muted-foreground">•</span>
                      <span className="text-xs text-muted-foreground">
                        {getChannelIcon(selectedConv.channel)} {selectedConv.channel.toUpperCase()}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-1">
                  <Button variant="ghost" size="sm" className="h-9 w-9 p-0">
                    <PhoneIcon className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="h-9 w-9 p-0">
                    <VideoIcon className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="h-9 w-9 p-0">
                    <MoreVerticalIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto px-6 py-4 space-y-3 bg-muted/10">
              {mockMessages.map((msg, index) => (
                <div
                  key={msg.id}
                  className={cn(
                    "flex items-end gap-2",
                    msg.direction === "outbound" ? "justify-end" : "justify-start"
                  )}
                >
                  {msg.direction === "inbound" && (
                    <Avatar className="w-7 h-7 mb-1">
                      <AvatarFallback className="text-xs bg-muted">
                        {selectedConv.contact.avatar}
                      </AvatarFallback>
                    </Avatar>
                  )}

                  <div className={cn(
                    "max-w-md px-4 py-3 rounded-2xl relative",
                    msg.direction === "outbound"
                      ? "bg-primary text-primary-foreground rounded-br-md"
                      : "bg-background border shadow-sm rounded-bl-md"
                  )}>
                    <p className="text-sm leading-relaxed">{msg.content}</p>
                    <div className={cn(
                      "flex items-center gap-1 mt-2",
                      msg.direction === "outbound" ? "justify-end text-primary-foreground/70" : "justify-end text-muted-foreground"
                    )}>
                      <span className="text-xs">{msg.timestamp}</span>
                      {msg.direction === "outbound" && getStatusIcon(msg.status)}
                    </div>
                  </div>

                  {msg.direction === "outbound" && (
                    <Avatar className="w-7 h-7 mb-1">
                      <AvatarFallback className="text-xs bg-primary/10">
                        A
                      </AvatarFallback>
                    </Avatar>
                  )}
                </div>
              ))}

              {/* Typing Indicator (when someone is typing) */}
              <div className="flex items-end gap-2 justify-start opacity-60">
                <Avatar className="w-7 h-7 mb-1">
                  <AvatarFallback className="text-xs bg-muted">
                    {selectedConv.contact.avatar}
                  </AvatarFallback>
                </Avatar>
                <div className="bg-background border shadow-sm rounded-2xl rounded-bl-md px-4 py-3">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Message Input */}
            <div className="border-t bg-background px-6 py-4">
              <div className="flex items-end gap-3">
                <div className="flex items-center gap-1">
                  <Button variant="ghost" size="sm" className="h-9 w-9 p-0 text-muted-foreground hover:text-foreground">
                    <PaperclipIcon className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="h-9 w-9 p-0 text-muted-foreground hover:text-foreground">
                    <SmileIcon className="h-4 w-4" />
                  </Button>
                </div>

                <div className="flex-1 relative">
                  <Input
                    placeholder="Digite uma mensagem..."
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === "Enter" && !e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage();
                      }
                    }}
                    className="min-h-[40px] resize-none border-muted-foreground/20 focus:border-primary rounded-full px-4 py-2"
                  />
                </div>

                <Button
                  onClick={handleSendMessage}
                  disabled={!message.trim()}
                  size="sm"
                  className={cn(
                    "h-9 w-9 p-0 rounded-full transition-all",
                    message.trim()
                      ? "bg-primary hover:bg-primary/90 text-primary-foreground"
                      : "bg-muted text-muted-foreground cursor-not-allowed"
                  )}
                >
                  <SendIcon className="h-4 w-4" />
                </Button>
              </div>

              {/* Quick Actions */}
              <div className="flex items-center gap-2 mt-3 text-xs text-muted-foreground">
                <span>Pressione Enter para enviar, Shift+Enter para nova linha</span>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <MessageSquareIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Selecione uma conversa</h3>
              <p className="text-muted-foreground">
                Escolha uma conversa da lista para começar a responder
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Right Sidebar - Contact Panel */}
      <div className="w-80 border-l bg-background/50">
        {selectedConv ? (
          <div className="h-full flex flex-col">
            {/* Contact Header */}
            <div className="p-6 border-b bg-background/80">
              <div className="text-center">
                <Avatar className="w-20 h-20 mx-auto mb-3 border-4 border-background shadow-lg">
                  <AvatarFallback className={cn(
                    "text-white font-medium text-xl",
                    selectedConv.channel === "whatsapp" ? "bg-green-500" :
                    selectedConv.channel === "sms" ? "bg-blue-500" : "bg-purple-500"
                  )}>
                    {selectedConv.contact.avatar}
                  </AvatarFallback>
                </Avatar>
                <h3 className="font-semibold text-lg mb-1">{selectedConv.contact.name}</h3>
                <p className="text-sm text-muted-foreground mb-2">{selectedConv.contact.email}</p>
                <div className="flex items-center justify-center gap-2">
                  <div className={cn(
                    "w-2 h-2 rounded-full",
                    selectedConv.contact.status === "online" ? "bg-green-500" :
                    selectedConv.contact.status === "away" ? "bg-yellow-500" : "bg-muted-foreground"
                  )} />
                  <span className="text-xs text-muted-foreground">
                    {selectedConv.contact.status === "online" ? "Online agora" :
                     selectedConv.contact.status === "away" ? "Ausente" : "Visto por último há 2h"}
                  </span>
                </div>
              </div>
            </div>

            {/* Contact Details */}
            <div className="flex-1 overflow-y-auto p-6 space-y-6">
              {/* Quick Actions */}
              <div className="grid grid-cols-2 gap-2">
                <Button variant="outline" size="sm" className="h-9">
                  <PhoneIcon className="h-4 w-4 mr-2" />
                  Ligar
                </Button>
                <Button variant="outline" size="sm" className="h-9">
                  <MailIcon className="h-4 w-4 mr-2" />
                  Email
                </Button>
              </div>

              <Separator />

              {/* Contact Information */}
              <div>
                <h4 className="font-medium mb-4 text-sm uppercase tracking-wide text-muted-foreground">Contato</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                    <div className="flex items-center gap-3">
                      <PhoneIcon className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">{selectedConv.contact.phone}</p>
                        <p className="text-xs text-muted-foreground">Telefone principal</p>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <PhoneIcon className="h-3 w-3" />
                    </Button>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                    <div className="flex items-center gap-3">
                      <MailIcon className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">{selectedConv.contact.email}</p>
                        <p className="text-xs text-muted-foreground">Email principal</p>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <MailIcon className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Conversation Status */}
              <div>
                <h4 className="font-medium mb-4 text-sm uppercase tracking-wide text-muted-foreground">Conversa</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Status</span>
                    {getStatusBadge(selectedConv.status)}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Canal</span>
                    <div className="flex items-center gap-1">
                      <span className="text-sm">{getChannelIcon(selectedConv.channel)}</span>
                      <span className="text-sm font-medium">{selectedConv.channel.toUpperCase()}</span>
                    </div>
                  </div>
                  {selectedConv.assignedTo && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Responsável</span>
                      <Badge variant="outline" className="text-xs">
                        <UserIcon className="h-3 w-3 mr-1" />
                        {selectedConv.assignedTo}
                      </Badge>
                    </div>
                  )}
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Última atividade</span>
                    <span className="text-sm font-medium">{selectedConv.timestamp}</span>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Tags */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium text-sm uppercase tracking-wide text-muted-foreground">Tags</h4>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <PlusIcon className="h-3 w-3" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {selectedConv.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>

              <Separator />

              {/* Notes Section */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium text-sm uppercase tracking-wide text-muted-foreground">Notas</h4>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <PlusIcon className="h-3 w-3" />
                  </Button>
                </div>
                <div className="text-sm text-muted-foreground">
                  Nenhuma nota adicionada ainda.
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full p-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-muted/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <UserIcon className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="font-medium mb-2">Detalhes do Contato</h3>
              <p className="text-sm text-muted-foreground max-w-xs">
                Selecione uma conversa para ver informações detalhadas do contato e histórico
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
